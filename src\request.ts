import { extend, ResponseError, RequestOptionsInit } from 'umi-request';
import { YTHToast, YTHLocalization, isEmpty } from 'yth-ui';
import type { RequestMethod } from 'umi-request';
import type { LocalizationType } from 'yth-ui/es/components/localization/index';
import { ConstHeaders, Setting, Token } from './Constant';

const codeMessage: Record<number, string> = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  405: '请求方法不被允许。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/** 异常处理程序
 * @see https://beta-pro.ant.design/docs/request-cn
 */
const errorHandler: (error: ResponseError) => void = (error: ResponseError) => {
  const { response } = error;
  if (response && response.status) {
    const errorText: string = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    if (status === 400) {
      YTHToast.show({
        type: 'error',
        messageText: `请求错误 【${response.statusText || errorText}】${status}`,
        p_props: { duration: 10 },
        m_props: { duration: 3000 },
      });
      return;
    }

    if (status === 401) {
      setTimeout(() => {
        window.location.href = `${window.location.origin}/user/login`;
      }, 2000);
    }

    if (status) {
      if (status === 401) {
        setTimeout(() => {
          window.location.href = `${window.location.origin}/user/login`;
        }, 2000);
      }
    }
    if (response.statusText !== '202') {
      YTHToast.show({
        type: 'error',
        messageText: `请求错误 【${errorText}】${status}: ${url}`,
        p_props: { duration: 10 },
        m_props: { duration: 3000 },
      });
    }
  }

  if (!response) {
    YTHToast.show({
      type: 'error',
      messageText: '您的网络发生异常，无法连接服务器',
      p_props: { duration: 10 },
      m_props: { duration: 3000 },
    });
  }
  throw error;
};

const requests: RequestMethod = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
});
requests.interceptors.request.use((url, options: RequestOptionsInit) => {
  const newOptions: RequestOptionsInit = {
    ...options,
    headers: { ...options.headers, ...ConstHeaders() },
  };
  return { url, options: newOptions };
});
export default requests;

const newConstHeaders: () => {
  'Content-Language'?: LocalizationType;
  Authorization?: string;
  affinity_host?: string;
} = () => {
  const { affinityHost = '' } = Setting();
  const headers: {
    'Content-Language'?: LocalizationType;
    Authorization?: string;
    affinity_host?: string;
  } = {};
  headers['Content-Language'] = YTHLocalization.getLanguage();
  if (Token()) {
    headers.Authorization = Token();
  }
  if (!isEmpty(affinityHost)) {
    headers.affinity_host = affinityHost;
  }
  return headers;
};

/**
 *@description 移动原本的请求方法-增加Content-Language
 * @param prefix 前缀
 * @returns
 */
export const getRequest: (
  prefix?: string,
  { token, affinityHost }?: { token?: string; affinityHost?: string },
) => RequestMethod = (prefix = '/gw/form-api', { token = '', affinityHost = '' } = {}) => {
  const headers: Record<string, string> = newConstHeaders();
  if (token) {
    headers.Authorization = token;
  }
  if (affinityHost) {
    headers.affinity_host = affinityHost;
  }

  const request: RequestMethod = extend({
    errorHandler, // 默认错误处理
    credentials: 'include', // 默认请求是否带上cookie
    timeout: 1000 * 20, // 默认请求超时时间
    prefix, // 前缀
    headers,
  });

  const blobs: string[] = [
    'application/x-download',
    'application/vnd.ms-excel',
    'application/msword',
    'application/octet-stream',
  ];

  request.interceptors.response.use(async (response, requestOptions) => {
    const contentType: string | null = response.headers.get('Content-Type');
    const isBlob: boolean = blobs.some((item) => contentType?.includes(item));

    if (isBlob) {
      // eslint-disable-next-line no-param-reassign
      requestOptions.responseType = 'blob';
      return response;
    }
    if (response.status !== 200) {
      const errObj: { response: { status: number; statusText: string } } = {
        response: {
          status: response.status,
          statusText: codeMessage[response.status],
        },
      };
      throw errObj;
    }

    const { error, code, status, msg, data } = await response.clone().json();

    if ((code === 200 || code === 202) && !error) return response;
    const errObject: {
      response: { status: number; statusText: string; data: Record<string, unknown> };
    } = {
      response: {
        status: code || status,
        statusText: msg,
        data,
      },
    };
    throw errObject;
  });

  return request;
};

const formRequest: RequestMethod = getRequest('/gw/form-api'); // 快开平台api
const rbacRequest: RequestMethod = getRequest('/gw/rbac-api/sys'); // 权限相关api
const baseRequest: RequestMethod = getRequest('/gw/base-api'); // 安全基础模块请求api
const smpRequest: RequestMethod = getRequest('/gw/smp-api'); // 第三方监控管理
// const monitorRequest = getRequest('/gw/campus-monitor-api'); // 园区监测
// const envRequest: RequestMethod = getRequest('/api'); // 生态环境监测
const envRequest: RequestMethod = getRequest('/gw/environment-api/'); // 生态环境监测
export {
  formRequest,
  baseRequest,
  rbacRequest,
  smpRequest,
  // monitorRequest,
  envRequest,
};
